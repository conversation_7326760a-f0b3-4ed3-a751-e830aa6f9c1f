/* Calculator Specific Styles */

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Glassmorphism effect */
.glass-effect {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mode-switcher {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 25px;
}

.mode-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    text-decoration: none;
    padding: 12px 28px;
    border-radius: 25px;
    border: 2px solid rgba(255, 255, 255, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.mode-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.mode-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.calculator-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    align-items: start;
}

.calculator-main {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    border-radius: 28px;
    padding: 40px;
    box-shadow:
        0 25px 80px rgba(102, 126, 234, 0.12),
        0 15px 35px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(102, 126, 234, 0.06);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
    min-height: 600px;
    display: flex;
    flex-direction: column;
}

.calculator-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.4), transparent);
    border-radius: 28px 28px 0 0;
}

.calculator-main::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.02) 0%, transparent 70%);
    animation: pulse 6s ease-in-out infinite;
    pointer-events: none;
}

.calculator-display {
    background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 24px;
    padding: 30px 35px;
    margin-bottom: 30px;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    box-shadow:
        inset 0 4px 15px rgba(0, 0, 0, 0.4),
        inset 0 -2px 8px rgba(255, 255, 255, 0.05),
        0 2px 0 rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.08);
}

.calculator-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.display-secondary {
    color: #999;
    font-size: 1.1rem;
    text-align: right;
    margin-bottom: 8px;
    min-height: 24px;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    opacity: 0.8;
}

.display-primary {
    color: #ffffff;
    font-size: 3.2rem;
    font-weight: 300;
    text-align: right;
    min-height: 70px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    word-wrap: break-word;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    letter-spacing: 1px;
}

.display-error {
    color: #ff6b6b;
    font-size: 1rem;
    text-align: right;
    margin-top: 8px;
    font-weight: 500;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.calculator-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    min-height: 35px;
    padding: 0 5px;
}

.memory-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 18px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.calculator-buttons {
    display: grid;
    gap: 15px;
    padding: 10px;
    flex: 1;
    align-content: start;
}

.basic-mode {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(6, 1fr);
}

.scientific-mode {
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 12px;
}

/* Special button layouts */
.btn-operator-tall {
    grid-row: span 2;
    font-size: 1.6rem !important;
}

/* Scientific mode specific adjustments */
.scientific-mode .btn {
    height: 65px;
    font-size: 1.1rem;
}

.scientific-mode .btn-function {
    font-size: 0.95rem;
    font-weight: 600;
}

.scientific-mode .btn-constant {
    font-size: 1.3rem;
    font-weight: 700;
}

.scientific-mode .btn-memory {
    font-size: 0.9rem;
    font-weight: 600;
}

.btn {
    border: none;
    border-radius: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.15);
    min-width: 75px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.12),
        0 4px 15px rgba(0, 0, 0, 0.08);
}

.btn:active {
    transform: translateY(-2px) scale(0.98);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-number {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    color: #2c3e50;
    border: 1px solid rgba(102, 126, 234, 0.12);
    font-weight: 700;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.06),
        0 2px 6px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.btn-number:hover {
    background: linear-gradient(145deg, #f8f9ff 0%, #e9ecff 100%);
    border-color: rgba(102, 126, 234, 0.25);
    color: #667eea;
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.08),
        0 4px 15px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.btn-operator {
    background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 700;
}

.btn-operator:hover {
    background: linear-gradient(145deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-equals {
    background: linear-gradient(145deg, #28a745 0%, #20c997 100%);
    color: white;
    font-weight: 700;
    font-size: 1.4rem;
}

.btn-equals:hover {
    background: linear-gradient(145deg, #218838 0%, #1ba085 100%);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-clear {
    background: linear-gradient(145deg, #dc3545 0%, #e74c3c 100%);
    color: white;
    font-weight: 700;
}

.btn-clear:hover {
    background: linear-gradient(145deg, #c82333 0%, #d63031 100%);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

.btn-function {
    background: linear-gradient(145deg, #6f42c1 0%, #8e44ad 100%);
    color: white;
    font-size: 1rem;
    font-weight: 600;
}

.btn-function:hover {
    background: linear-gradient(145deg, #5a32a3 0%, #7d3c98 100%);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.4);
}

.btn-constant {
    background: linear-gradient(145deg, #fd7e14 0%, #f39c12 100%);
    color: white;
    font-weight: 700;
}

.btn-constant:hover {
    background: linear-gradient(145deg, #e8690b 0%, #e67e22 100%);
    box-shadow: 0 8px 25px rgba(253, 126, 20, 0.4);
}

.btn-memory {
    background: linear-gradient(145deg, #20c997 0%, #17a2b8 100%);
    color: white;
    font-size: 1rem;
    font-weight: 600;
}

.btn-memory:hover {
    background: linear-gradient(145deg, #1ba085 0%, #138496 100%);
    box-shadow: 0 8px 25px rgba(32, 201, 151, 0.4);
}

/* History Panel */
.calculator-history {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    border-radius: 28px;
    padding: 35px;
    box-shadow:
        0 25px 80px rgba(102, 126, 234, 0.12),
        0 15px 35px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(102, 126, 234, 0.06);
    height: 600px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out 0.2s both;
}

.calculator-history::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    position: relative;
}

.history-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

.history-header h3 {
    color: #667eea;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(102, 126, 234, 0.1);
}

.btn-clear-history {
    background: linear-gradient(145deg, #dc3545 0%, #e74c3c 100%);
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

.btn-clear-history:hover {
    background: linear-gradient(145deg, #c82333 0%, #d63031 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
}

.history-list {
    flex: 1;
    overflow-y: auto;
    max-height: 450px;
    padding-right: 5px;
}

.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: linear-gradient(145deg, #667eea, #764ba2);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(145deg, #5a6fd8, #6a4190);
}

.history-item {
    background: linear-gradient(145deg, #f8f9ff 0%, #ffffff 100%);
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 16px;
    padding: 18px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.05);
}

.history-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(145deg, #667eea, #764ba2);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.history-item:hover::before {
    transform: scaleY(1);
}

.history-item:hover {
    background: linear-gradient(145deg, #e9ecff 0%, #f8f9ff 100%);
    border-color: rgba(102, 126, 234, 0.3);
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.history-expression {
    color: #666;
    font-size: 0.95rem;
    margin-bottom: 8px;
    font-family: 'Courier New', monospace;
    opacity: 0.8;
}

.history-result {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

.history-time {
    color: #999;
    font-size: 0.8rem;
    text-align: right;
    margin-top: 8px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .calculator-wrapper {
        grid-template-columns: 1.8fr 1fr;
        gap: 30px;
    }
}

@media (max-width: 1024px) {
    .calculator-wrapper {
        grid-template-columns: 1.5fr 1fr;
        gap: 25px;
    }

    .calculator-main {
        padding: 35px;
        min-height: 550px;
    }

    .calculator-history {
        height: 550px;
        padding: 30px;
    }

    .btn {
        height: 70px;
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .calculator-wrapper {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 0 15px;
    }

    .calculator-main {
        padding: 30px;
        min-height: auto;
    }

    .calculator-history {
        order: -1;
        height: 400px;
        padding: 25px;
    }

    .calculator-display {
        min-height: 120px;
        padding: 25px 30px;
    }

    .display-primary {
        font-size: 2.6rem;
        min-height: 60px;
    }

    .btn {
        height: 65px;
        font-size: 1.2rem;
    }

    .scientific-mode {
        grid-template-columns: repeat(4, 1fr);
    }

    .calculator-buttons {
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .calculator-wrapper {
        padding: 0 10px;
        gap: 20px;
    }

    .calculator-main,
    .calculator-history {
        padding: 25px;
        border-radius: 24px;
    }

    .calculator-history {
        height: 350px;
    }

    .calculator-display {
        padding: 25px;
        margin-bottom: 25px;
        min-height: 110px;
        border-radius: 20px;
    }

    .calculator-buttons {
        gap: 10px;
        padding: 5px;
    }

    .btn {
        height: 60px;
        font-size: 1.1rem;
        border-radius: 16px;
        min-width: 60px;
    }

    .display-primary {
        font-size: 2.4rem;
        min-height: 55px;
    }

    .display-secondary {
        font-size: 1rem;
        min-height: 20px;
    }

    .mode-switcher {
        flex-direction: row;
        gap: 12px;
    }

    .mode-btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .history-header h3 {
        font-size: 1.3rem;
    }

    .history-item {
        padding: 16px;
    }

    .history-result {
        font-size: 1.1rem;
    }
}

@media (max-width: 360px) {
    .calculator-wrapper {
        gap: 15px;
    }

    .calculator-main,
    .calculator-history {
        padding: 20px;
    }

    .calculator-buttons {
        gap: 8px;
    }

    .btn {
        height: 55px;
        font-size: 1rem;
        min-width: 55px;
    }

    .display-primary {
        font-size: 2rem;
    }

    .calculator-display {
        min-height: 100px;
        padding: 20px;
    }

    .scientific-mode {
        grid-template-columns: repeat(3, 1fr);
    }
}
