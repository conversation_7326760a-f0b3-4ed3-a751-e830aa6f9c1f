/* Calculator Specific Styles */

.mode-switcher {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.mode-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 8px 20px;
    border-radius: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    font-weight: 500;
}

.mode-btn.active,
.mode-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
}

.calculator-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.calculator-main {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.calculator-display {
    background: #1a1a1a;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    position: relative;
}

.display-secondary {
    color: #888;
    font-size: 1rem;
    text-align: right;
    margin-bottom: 5px;
    min-height: 20px;
    word-wrap: break-word;
}

.display-primary {
    color: white;
    font-size: 2.5rem;
    font-weight: 300;
    text-align: right;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    word-wrap: break-word;
}

.display-error {
    color: #ff4757;
    font-size: 1rem;
    text-align: right;
    margin-top: 5px;
}

.calculator-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    min-height: 25px;
}

.memory-indicator {
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.calculator-buttons {
    display: grid;
    gap: 10px;
}

.basic-mode {
    grid-template-columns: repeat(4, 1fr);
}

.scientific-mode {
    grid-template-columns: repeat(5, 1fr);
}

.btn {
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.btn-number {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e9ecef;
}

.btn-number:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.btn-operator {
    background: #667eea;
    color: white;
}

.btn-operator:hover {
    background: #5a6fd8;
}

.btn-equals {
    background: #28a745;
    color: white;
}

.btn-equals:hover {
    background: #218838;
}

.btn-clear {
    background: #dc3545;
    color: white;
}

.btn-clear:hover {
    background: #c82333;
}

.btn-function {
    background: #6f42c1;
    color: white;
    font-size: 1rem;
}

.btn-function:hover {
    background: #5a32a3;
}

.btn-constant {
    background: #fd7e14;
    color: white;
}

.btn-constant:hover {
    background: #e8690b;
}

.btn-memory {
    background: #20c997;
    color: white;
    font-size: 1rem;
}

.btn-memory:hover {
    background: #1ba085;
}

/* History Panel */
.calculator-history {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.history-header h3 {
    color: #667eea;
    margin: 0;
    font-size: 1.3rem;
}

.btn-clear-history {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-clear-history:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.history-list {
    flex: 1;
    overflow-y: auto;
    max-height: 400px;
}

.history-item {
    background: #f8f9ff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: #e9ecff;
    border-color: #667eea;
    transform: translateX(5px);
}

.history-expression {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.history-result {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.history-time {
    color: #999;
    font-size: 0.8rem;
    text-align: right;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calculator-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .calculator-main {
        padding: 20px;
    }
    
    .display-primary {
        font-size: 2rem;
    }
    
    .btn {
        height: 50px;
        font-size: 1rem;
    }
    
    .scientific-mode {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .calculator-history {
        order: -1;
        max-height: 300px;
    }
}

@media (max-width: 480px) {
    .calculator-buttons {
        gap: 8px;
    }
    
    .btn {
        height: 45px;
        font-size: 0.9rem;
    }
    
    .display-primary {
        font-size: 1.8rem;
    }
    
    .mode-switcher {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }
}
