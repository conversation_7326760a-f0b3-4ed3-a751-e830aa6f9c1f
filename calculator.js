class AdvancedCalculator {
    constructor() {
        this.expression = '';
        this.result = '0';
        this.lastResult = 0;
        this.memory = 0;
        this.isNewCalculation = true;
        
        this.initializeElements();
        this.attachEventListeners();
        this.loadHistory();
        this.updateMemoryIndicator();
    }
    
    initializeElements() {
        this.expressionDisplay = document.getElementById('expression');
        this.resultDisplay = document.getElementById('result');
        this.errorDisplay = document.getElementById('error');
        this.memoryIndicator = document.getElementById('memoryIndicator');
        this.memoryValue = document.getElementById('memoryValue');
        this.historyList = document.getElementById('historyList');
        this.clearHistoryBtn = document.getElementById('clearHistory');
    }
    
    attachEventListeners() {
        // Number buttons
        document.querySelectorAll('.btn-number').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addButtonEffect(btn);
                const number = btn.dataset.number;
                this.inputNumber(number);
            });
        });
        
        // Operator buttons
        document.querySelectorAll('.btn-operator').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addButtonEffect(btn);
                const operator = btn.dataset.operator;
                this.inputOperator(operator);
            });
        });
        
        // Function buttons
        document.querySelectorAll('.btn-function').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addButtonEffect(btn);
                const func = btn.dataset.function;
                this.inputFunction(func);
            });
        });

        // Constant buttons
        document.querySelectorAll('.btn-constant').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addButtonEffect(btn);
                const value = btn.dataset.value;
                this.inputConstant(value);
            });
        });

        // Memory buttons
        document.querySelectorAll('.btn-memory').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addButtonEffect(btn);
                const operation = btn.dataset.memory;
                this.handleMemory(operation);
            });
        });

        // Action buttons
        document.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addButtonEffect(btn);
                const action = btn.dataset.action;
                this.handleAction(action);
            });
        });
        
        // Clear history button
        this.clearHistoryBtn.addEventListener('click', () => {
            this.clearHistory();
        });
        
        // Keyboard support
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    
    inputNumber(number) {
        if (this.isNewCalculation) {
            this.expression = '';
            this.isNewCalculation = false;
        }
        
        if (number === '.' && this.expression.includes('.')) {
            return; // Prevent multiple decimal points
        }
        
        this.expression += number;
        this.updateDisplay();
    }
    
    inputOperator(operator) {
        if (this.isNewCalculation) {
            this.expression = this.result;
            this.isNewCalculation = false;
        }
        
        // Replace last operator if expression ends with operator
        if (/[+\-*/]$/.test(this.expression)) {
            this.expression = this.expression.slice(0, -1);
        }
        
        this.expression += operator;
        this.updateDisplay();
    }
    
    inputFunction(func) {
        if (this.isNewCalculation) {
            this.expression = this.result;
            this.isNewCalculation = false;
        }
        
        const currentValue = parseFloat(this.result) || 0;
        this.calculateFunction(func, currentValue);
    }
    
    inputConstant(value) {
        if (this.isNewCalculation) {
            this.expression = '';
            this.isNewCalculation = false;
        }
        
        this.expression += value;
        this.updateDisplay();
    }
    
    handleAction(action) {
        switch (action) {
            case 'calculate':
                this.calculate();
                break;
            case 'clear-all':
                this.clearAll();
                break;
            case 'clear-entry':
                this.clearEntry();
                break;
            case 'backspace':
                this.backspace();
                break;
        }
    }
    
    calculate() {
        if (!this.expression) return;
        
        this.sendRequest('calculate', { expression: this.expression })
            .then(response => {
                if (response.success) {
                    this.result = response.result;
                    this.lastResult = parseFloat(response.result);
                    this.isNewCalculation = true;
                    this.loadHistory();
                } else {
                    this.showError(response.error);
                }
                this.updateDisplay();
            });
    }
    
    calculateFunction(func, value) {
        this.sendRequest('function', { function: func, value: value })
            .then(response => {
                if (response.success) {
                    this.result = response.result;
                    this.expression = response.result;
                    this.lastResult = parseFloat(response.result);
                    this.loadHistory();
                } else {
                    this.showError(response.error);
                }
                this.updateDisplay();
            });
    }
    
    handleMemory(operation) {
        const currentValue = parseFloat(this.result) || 0;
        
        this.sendRequest('memory', { operation: operation, value: currentValue })
            .then(response => {
                if (response.success) {
                    if (operation === 'recall') {
                        this.result = response.result.toString();
                        this.expression = this.result;
                        this.updateDisplay();
                    }
                    this.updateMemoryIndicator();
                }
            });
    }
    
    clearAll() {
        this.expression = '';
        this.result = '0';
        this.isNewCalculation = true;
        this.hideError();
        this.updateDisplay();
    }
    
    clearEntry() {
        this.expression = '';
        this.updateDisplay();
    }
    
    backspace() {
        if (this.expression.length > 0) {
            this.expression = this.expression.slice(0, -1);
            if (this.expression === '') {
                this.result = '0';
            }
            this.updateDisplay();
        }
    }
    
    updateDisplay() {
        this.expressionDisplay.textContent = this.expression || '';
        this.resultDisplay.textContent = this.result;

        // Add animation to result display when value changes
        this.resultDisplay.style.animation = 'none';
        setTimeout(() => {
            this.resultDisplay.style.animation = 'fadeIn 0.3s ease-in-out';
        }, 10);
    }
    
    showError(message) {
        this.errorDisplay.textContent = message;
        this.errorDisplay.style.display = 'block';
        setTimeout(() => this.hideError(), 3000);
    }
    
    hideError() {
        this.errorDisplay.style.display = 'none';
    }
    
    updateMemoryIndicator() {
        this.sendRequest('memory', { operation: 'recall' })
            .then(response => {
                if (response.success) {
                    const memValue = parseFloat(response.result);
                    if (memValue !== 0) {
                        this.memoryValue.textContent = memValue;
                        this.memoryIndicator.style.display = 'block';
                    } else {
                        this.memoryIndicator.style.display = 'none';
                    }
                }
            });
    }
    
    loadHistory() {
        this.sendRequest('history', {})
            .then(response => {
                if (response.success) {
                    this.displayHistory(response.result);
                }
            });
    }
    
    displayHistory(history) {
        this.historyList.innerHTML = '';
        
        if (history.length === 0) {
            this.historyList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">Belum ada riwayat</div>';
            return;
        }
        
        history.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div class="history-expression">${item.perhitungan}</div>
                <div class="history-result">= ${item.hasil}</div>
                <div class="history-time">${item.waktu}</div>
            `;
            
            historyItem.addEventListener('click', () => {
                this.result = item.hasil;
                this.expression = item.hasil;
                this.updateDisplay();
            });
            
            this.historyList.appendChild(historyItem);
        });
    }
    
    clearHistory() {
        this.sendRequest('history', { operation: 'clear' })
            .then(response => {
                if (response.success) {
                    this.loadHistory();
                }
            });
    }
    
    handleKeyboard(e) {
        e.preventDefault();
        
        if (e.key >= '0' && e.key <= '9' || e.key === '.') {
            this.inputNumber(e.key);
        } else if (['+', '-', '*', '/'].includes(e.key)) {
            this.inputOperator(e.key);
        } else if (e.key === 'Enter' || e.key === '=') {
            this.calculate();
        } else if (e.key === 'Escape') {
            this.clearAll();
        } else if (e.key === 'Backspace') {
            this.backspace();
        }
    }
    
    addButtonEffect(button) {
        // Add visual feedback for button press
        button.style.transform = 'scale(0.95)';
        button.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';

        setTimeout(() => {
            button.style.transform = '';
            button.style.boxShadow = '';
        }, 150);
    }

    async sendRequest(action, data) {
        try {
            const response = await fetch('kalkulator.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: action,
                    ...data
                })
            });

            return await response.json();
        } catch (error) {
            console.error('Request failed:', error);
            return { success: false, error: 'Network error' };
        }
    }
}

// Initialize calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdvancedCalculator();
});
