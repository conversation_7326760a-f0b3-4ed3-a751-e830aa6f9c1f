<?php
// Fungsi untuk operasi kalkulator
function tambah($a, $b) {
    return $a + $b;
}

function kurang($a, $b) {
    return $a - $b;
}

function kali($a, $b) {
    return $a * $b;
}

function bagi($a, $b) {
    if ($b == 0) {
        return "Error: Pembagian dengan nol tidak diperbolehkan";
    }
    return $a / $b;
}

function hitung($angka1, $operator, $angka2) {
    switch ($operator) {
        case '+':
            return tambah($angka1, $angka2);
        case '-':
            return kurang($angka1, $angka2);
        case '*':
            return kali($angka1, $angka2);
        case '/':
            return bagi($angka1, $angka2);
        default:
            return "Error: Operator tidak valid";
    }
}

// Fungsi untuk konversi suhu
function celsiusToFahrenheit($celsius) {
    return ($celsius * 9/5) + 32;
}

function celsiusToKelvin($celsius) {
    return $celsius + 273.15;
}

function fahrenheitToCelsius($fahrenheit) {
    return ($fahrenheit - 32) * 5/9;
}

function fahrenheitToKelvin($fahrenheit) {
    return (($fahrenheit - 32) * 5/9) + 273.15;
}

function kelvinToCelsius($kelvin) {
    return $kelvin - 273.15;
}

function kelvinToFahrenheit($kelvin) {
    return (($kelvin - 273.15) * 9/5) + 32;
}

function konversiSuhu($nilai, $dari, $ke) {
    $nilai = floatval($nilai);
    
    // Konversi dari Celsius
    if ($dari == 'celsius') {
        if ($ke == 'fahrenheit') {
            return celsiusToFahrenheit($nilai);
        } elseif ($ke == 'kelvin') {
            return celsiusToKelvin($nilai);
        } else {
            return $nilai; // celsius ke celsius
        }
    }
    
    // Konversi dari Fahrenheit
    if ($dari == 'fahrenheit') {
        if ($ke == 'celsius') {
            return fahrenheitToCelsius($nilai);
        } elseif ($ke == 'kelvin') {
            return fahrenheitToKelvin($nilai);
        } else {
            return $nilai; // fahrenheit ke fahrenheit
        }
    }
    
    // Konversi dari Kelvin
    if ($dari == 'kelvin') {
        if ($ke == 'celsius') {
            return kelvinToCelsius($nilai);
        } elseif ($ke == 'fahrenheit') {
            return kelvinToFahrenheit($nilai);
        } else {
            return $nilai; // kelvin ke kelvin
        }
    }
    
    return "Error: Unit suhu tidak valid";
}

// Fungsi untuk memformat angka
function formatAngka($angka) {
    if (is_numeric($angka)) {
        return number_format($angka, 2, ',', '.');
    }
    return $angka;
}

// Fungsi untuk validasi input angka
function validasiAngka($input) {
    return is_numeric($input) && !empty($input);
}
?>
