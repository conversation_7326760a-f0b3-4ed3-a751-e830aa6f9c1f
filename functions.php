<?php
// Fungsi untuk operasi kalkulator dasar
function tambah($a, $b) {
    return $a + $b;
}

function kurang($a, $b) {
    return $a - $b;
}

function kali($a, $b) {
    return $a * $b;
}

function bagi($a, $b) {
    if ($b == 0) {
        return "Error: Pembagian dengan nol tidak diperbolehkan";
    }
    return $a / $b;
}

// Fungsi untuk operasi matematika lanjutan
function pangkat($a, $b) {
    return pow($a, $b);
}

function akarKuadrat($a) {
    if ($a < 0) {
        return "Error: Tidak dapat menghitung akar kuadrat dari bilangan negatif";
    }
    return sqrt($a);
}

function persen($a, $b) {
    return ($a * $b) / 100;
}

function sinus($a) {
    return sin(deg2rad($a));
}

function cosinus($a) {
    return cos(deg2rad($a));
}

function tangen($a) {
    if (cos(deg2rad($a)) == 0) {
        return "Error: Tangen tidak terdefinisi";
    }
    return tan(deg2rad($a));
}

function logaritma($a) {
    if ($a <= 0) {
        return "Error: Logaritma hanya terdefinisi untuk bilangan positif";
    }
    return log10($a);
}

function logaritmaNatural($a) {
    if ($a <= 0) {
        return "Error: Logaritma natural hanya terdefinisi untuk bilangan positif";
    }
    return log($a);
}

function faktorial($n) {
    if ($n < 0) {
        return "Error: Faktorial tidak terdefinisi untuk bilangan negatif";
    }
    if ($n > 170) {
        return "Error: Angka terlalu besar untuk faktorial";
    }
    if ($n == 0 || $n == 1) {
        return 1;
    }
    $result = 1;
    for ($i = 2; $i <= $n; $i++) {
        $result *= $i;
    }
    return $result;
}

function hitung($angka1, $operator, $angka2 = null) {
    switch ($operator) {
        case '+':
            return tambah($angka1, $angka2);
        case '-':
            return kurang($angka1, $angka2);
        case '*':
            return kali($angka1, $angka2);
        case '/':
            return bagi($angka1, $angka2);
        case '^':
        case '**':
            return pangkat($angka1, $angka2);
        case '%':
            return persen($angka1, $angka2);
        case 'sqrt':
            return akarKuadrat($angka1);
        case 'sin':
            return sinus($angka1);
        case 'cos':
            return cosinus($angka1);
        case 'tan':
            return tangen($angka1);
        case 'log':
            return logaritma($angka1);
        case 'ln':
            return logaritmaNatural($angka1);
        case '!':
            return faktorial($angka1);
        default:
            return "Error: Operator tidak valid";
    }
}

// Fungsi untuk evaluasi ekspresi matematika yang aman
function evaluateExpression($expression) {
    // Membersihkan expression dari karakter yang tidak diinginkan
    $expression = preg_replace('/[^0-9+\-*\/\(\)\.]/', '', $expression);

    // Validasi dasar
    if (empty($expression)) {
        return "Error: Ekspresi kosong";
    }

    // Cek keseimbangan tanda kurung
    if (substr_count($expression, '(') !== substr_count($expression, ')')) {
        return "Error: Tanda kurung tidak seimbang";
    }

    // Cek operator berturut-turut
    if (preg_match('/[+\-*\/]{2,}/', $expression)) {
        return "Error: Operator berturut-turut tidak valid";
    }

    // Cek pembagian dengan nol
    if (preg_match('/\/\s*0(?!\d)/', $expression)) {
        return "Error: Pembagian dengan nol tidak diperbolehkan";
    }

    try {
        // Evaluasi expression menggunakan eval dengan validasi ketat
        $result = @eval("return $expression;");

        if ($result === false || $result === null) {
            return "Error: Ekspresi tidak valid";
        }

        // Cek hasil infinite atau NaN
        if (!is_finite($result)) {
            return "Error: Hasil tidak terdefinisi";
        }

        return $result;
    } catch (ParseError $e) {
        return "Error: Sintaks tidak valid";
    } catch (Error $e) {
        return "Error: Perhitungan gagal";
    } catch (Exception $e) {
        return "Error: " . $e->getMessage();
    }
}

// Fungsi untuk konversi suhu
function celsiusToFahrenheit($celsius) {
    return ($celsius * 9/5) + 32;
}

function celsiusToKelvin($celsius) {
    return $celsius + 273.15;
}

function fahrenheitToCelsius($fahrenheit) {
    return ($fahrenheit - 32) * 5/9;
}

function fahrenheitToKelvin($fahrenheit) {
    return (($fahrenheit - 32) * 5/9) + 273.15;
}

function kelvinToCelsius($kelvin) {
    return $kelvin - 273.15;
}

function kelvinToFahrenheit($kelvin) {
    return (($kelvin - 273.15) * 9/5) + 32;
}

function konversiSuhu($nilai, $dari, $ke) {
    $nilai = floatval($nilai);
    
    // Konversi dari Celsius
    if ($dari == 'celsius') {
        if ($ke == 'fahrenheit') {
            return celsiusToFahrenheit($nilai);
        } elseif ($ke == 'kelvin') {
            return celsiusToKelvin($nilai);
        } else {
            return $nilai; // celsius ke celsius
        }
    }
    
    // Konversi dari Fahrenheit
    if ($dari == 'fahrenheit') {
        if ($ke == 'celsius') {
            return fahrenheitToCelsius($nilai);
        } elseif ($ke == 'kelvin') {
            return fahrenheitToKelvin($nilai);
        } else {
            return $nilai; // fahrenheit ke fahrenheit
        }
    }
    
    // Konversi dari Kelvin
    if ($dari == 'kelvin') {
        if ($ke == 'celsius') {
            return kelvinToCelsius($nilai);
        } elseif ($ke == 'fahrenheit') {
            return kelvinToFahrenheit($nilai);
        } else {
            return $nilai; // kelvin ke kelvin
        }
    }
    
    return "Error: Unit suhu tidak valid";
}

// Fungsi untuk memformat angka
function formatAngka($angka) {
    if (is_numeric($angka)) {
        return number_format($angka, 2, ',', '.');
    }
    return $angka;
}

// Fungsi untuk validasi input angka
function validasiAngka($input) {
    return is_numeric($input) && !empty($input);
}

// Fungsi untuk mengelola riwayat perhitungan
function tambahRiwayat($perhitungan, $hasil) {
    if (!isset($_SESSION['riwayat'])) {
        $_SESSION['riwayat'] = [];
    }

    $entry = [
        'perhitungan' => $perhitungan,
        'hasil' => $hasil,
        'waktu' => date('H:i:s')
    ];

    array_unshift($_SESSION['riwayat'], $entry);

    // Batasi riwayat maksimal 10 entri
    if (count($_SESSION['riwayat']) > 10) {
        $_SESSION['riwayat'] = array_slice($_SESSION['riwayat'], 0, 10);
    }
}

function getRiwayat() {
    return $_SESSION['riwayat'] ?? [];
}

function hapusRiwayat() {
    $_SESSION['riwayat'] = [];
}

// Fungsi untuk mengelola memory kalkulator
function setMemory($value) {
    $_SESSION['memory'] = $value;
}

function getMemory() {
    return $_SESSION['memory'] ?? 0;
}

function addToMemory($value) {
    $current = getMemory();
    setMemory($current + $value);
}

function subtractFromMemory($value) {
    $current = getMemory();
    setMemory($current - $value);
}

function clearMemory() {
    $_SESSION['memory'] = 0;
}

// Fungsi untuk memformat angka dengan lebih baik
function formatAngkaAdvanced($angka, $desimal = 6) {
    if (!is_numeric($angka)) {
        return $angka;
    }

    // Jika angka sangat kecil atau sangat besar, gunakan notasi ilmiah
    if (abs($angka) >= 1e12 || (abs($angka) < 1e-6 && $angka != 0)) {
        return sprintf('%.3e', $angka);
    }

    // Hapus trailing zeros
    $formatted = rtrim(rtrim(number_format($angka, $desimal, '.', ''), '0'), '.');

    return $formatted;
}

// Fungsi untuk validasi ekspresi matematika
function validasiEkspresi($expression) {
    // Cek karakter yang diizinkan
    if (!preg_match('/^[0-9+\-*\/\(\)\.\^√π\s]*$/', $expression)) {
        return false;
    }

    // Cek keseimbangan tanda kurung
    $open = substr_count($expression, '(');
    $close = substr_count($expression, ')');

    return $open === $close;
}

// Fungsi untuk konstanta matematika
function getKonstanta($nama) {
    switch ($nama) {
        case 'pi':
        case 'π':
            return M_PI;
        case 'e':
            return M_E;
        default:
            return 0;
    }
}
?>
