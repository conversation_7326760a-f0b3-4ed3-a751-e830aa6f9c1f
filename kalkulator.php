<?php
require_once 'functions.php';

$hasil = '';
$angka1 = '';
$angka2 = '';
$operator = '';
$error = '';

if ($_POST) {
    $angka1 = $_POST['angka1'] ?? '';
    $angka2 = $_POST['angka2'] ?? '';
    $operator = $_POST['operator'] ?? '';
    
    if (validasiAngka($angka1) && validasiAngka($angka2)) {
        $hasil = hitung(floatval($angka1), $operator, floatval($angka2));
        if (is_numeric($hasil)) {
            $hasil = formatAngka($hasil);
        }
    } else {
        $error = "Mohon masukkan angka yang valid";
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalkulator - Aplikasi Kalkulator & Konvers<PERSON> Suhu</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧮 Kalkulator</h1>
            <nav class="breadcrumb">
                <a href="index.php">Beranda</a> > Kalkulator
            </nav>
        </header>

        <div class="calculator-container">
            <form method="POST" class="calculator-form">
                <div class="input-group">
                    <label for="angka1">Angka Pertama:</label>
                    <input type="number" 
                           id="angka1" 
                           name="angka1" 
                           step="any" 
                           value="<?php echo htmlspecialchars($angka1); ?>" 
                           required>
                </div>

                <div class="input-group">
                    <label for="operator">Operator:</label>
                    <select id="operator" name="operator" required>
                        <option value="">Pilih Operator</option>
                        <option value="+" <?php echo ($operator == '+') ? 'selected' : ''; ?>>+ (Penjumlahan)</option>
                        <option value="-" <?php echo ($operator == '-') ? 'selected' : ''; ?>>- (Pengurangan)</option>
                        <option value="*" <?php echo ($operator == '*') ? 'selected' : ''; ?>>× (Perkalian)</option>
                        <option value="/" <?php echo ($operator == '/') ? 'selected' : ''; ?>>÷ (Pembagian)</option>
                    </select>
                </div>

                <div class="input-group">
                    <label for="angka2">Angka Kedua:</label>
                    <input type="number" 
                           id="angka2" 
                           name="angka2" 
                           step="any" 
                           value="<?php echo htmlspecialchars($angka2); ?>" 
                           required>
                </div>

                <button type="submit" class="btn-calculate">Hitung</button>
            </form>

            <?php if ($error): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($hasil !== '' && !$error): ?>
                <div class="result-container">
                    <h3>Hasil Perhitungan:</h3>
                    <div class="result">
                        <?php echo htmlspecialchars($angka1); ?> 
                        <?php echo htmlspecialchars($operator); ?> 
                        <?php echo htmlspecialchars($angka2); ?> 
                        = <strong><?php echo htmlspecialchars($hasil); ?></strong>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="navigation">
            <a href="index.php" class="btn-nav">← Kembali ke Beranda</a>
            <a href="konversi_suhu.php" class="btn-nav">Konversi Suhu →</a>
        </div>
    </div>
</body>
</html>
