<?php
session_start();
require_once 'functions.php';

$display = '0';
$error = '';
$mode = $_GET['mode'] ?? 'basic'; // basic atau scientific

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    $action = $_POST['action'];
    $response = ['success' => false, 'result' => '', 'error' => ''];

    switch ($action) {
        case 'calculate':
            $expression = $_POST['expression'] ?? '';
            if (!empty($expression)) {
                try {
                    $result = evaluateExpression($expression);
                    if (is_numeric($result)) {
                        $formatted_result = formatAngkaAdvanced($result);
                        tambahRiwayat($expression, $formatted_result);
                        $response = ['success' => true, 'result' => $formatted_result];
                    } else {
                        $response = ['success' => false, 'error' => $result];
                    }
                } catch (Exception $e) {
                    $response = ['success' => false, 'error' => 'Error dalam perhitungan'];
                }
            }
            break;

        case 'function':
            $func = $_POST['function'] ?? '';
            $value = floatval($_POST['value'] ?? 0);

            $result = hitung($value, $func);
            if (is_numeric($result)) {
                $formatted_result = formatAngkaAdvanced($result);
                tambahRiwayat("$func($value)", $formatted_result);
                $response = ['success' => true, 'result' => $formatted_result];
            } else {
                $response = ['success' => false, 'error' => $result];
            }
            break;

        case 'memory':
            $operation = $_POST['operation'] ?? '';
            $value = floatval($_POST['value'] ?? 0);

            switch ($operation) {
                case 'store':
                    setMemory($value);
                    $response = ['success' => true, 'result' => 'Memory stored'];
                    break;
                case 'recall':
                    $response = ['success' => true, 'result' => getMemory()];
                    break;
                case 'add':
                    addToMemory($value);
                    $response = ['success' => true, 'result' => 'Added to memory'];
                    break;
                case 'subtract':
                    subtractFromMemory($value);
                    $response = ['success' => true, 'result' => 'Subtracted from memory'];
                    break;
                case 'clear':
                    clearMemory();
                    $response = ['success' => true, 'result' => 'Memory cleared'];
                    break;
            }
            break;

        case 'history':
            $operation = $_POST['operation'] ?? '';
            if ($operation === 'clear') {
                hapusRiwayat();
                $response = ['success' => true, 'result' => 'History cleared'];
            } else {
                $response = ['success' => true, 'result' => getRiwayat()];
            }
            break;
    }

    echo json_encode($response);
    exit;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalkulator Advanced - Aplikasi Kalkulator & Konversi Suhu</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="calculator.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧮 Kalkulator Advanced</h1>
            <nav class="breadcrumb">
                <a href="index.php">Beranda</a> > Kalkulator
            </nav>

            <div class="mode-switcher">
                <a href="?mode=basic" class="mode-btn <?php echo $mode === 'basic' ? 'active' : ''; ?>">Basic</a>
                <a href="?mode=scientific" class="mode-btn <?php echo $mode === 'scientific' ? 'active' : ''; ?>">Scientific</a>
            </div>
        </header>

        <div class="calculator-wrapper">
            <div class="calculator-main">
                <!-- Display -->
                <div class="calculator-display">
                    <div class="display-secondary" id="expression"></div>
                    <div class="display-primary" id="result">0</div>
                    <div class="display-error" id="error" style="display: none;"></div>
                </div>

                <!-- Memory & History Info -->
                <div class="calculator-info">
                    <div class="memory-indicator" id="memoryIndicator" style="display: none;">
                        M: <span id="memoryValue">0</span>
                    </div>
                </div>

                <!-- Button Grid -->
                <div class="calculator-buttons <?php echo $mode; ?>-mode">
                    <?php if ($mode === 'scientific'): ?>
                    <!-- Scientific Functions Row 1 -->
                    <button class="btn btn-function" data-function="sin">sin</button>
                    <button class="btn btn-function" data-function="cos">cos</button>
                    <button class="btn btn-function" data-function="tan">tan</button>
                    <button class="btn btn-function" data-function="log">log</button>
                    <button class="btn btn-function" data-function="ln">ln</button>

                    <!-- Scientific Functions Row 2 -->
                    <button class="btn btn-function" data-function="sqrt">√</button>
                    <button class="btn btn-function" data-function="^">x^y</button>
                    <button class="btn btn-function" data-function="!">x!</button>
                    <button class="btn btn-constant" data-value="3.14159">π</button>
                    <button class="btn btn-constant" data-value="2.71828">e</button>
                    <?php endif; ?>

                    <!-- Memory Functions -->
                    <button class="btn btn-memory" data-memory="clear">MC</button>
                    <button class="btn btn-memory" data-memory="recall">MR</button>
                    <button class="btn btn-memory" data-memory="add">M+</button>
                    <button class="btn btn-memory" data-memory="subtract">M-</button>
                    <button class="btn btn-memory" data-memory="store">MS</button>

                    <!-- Clear Functions -->
                    <button class="btn btn-clear" data-action="clear-all">C</button>
                    <button class="btn btn-clear" data-action="clear-entry">CE</button>
                    <button class="btn btn-clear" data-action="backspace">⌫</button>
                    <button class="btn btn-operator" data-operator="/">/</button>

                    <!-- Number Pad -->
                    <button class="btn btn-number" data-number="7">7</button>
                    <button class="btn btn-number" data-number="8">8</button>
                    <button class="btn btn-number" data-number="9">9</button>
                    <button class="btn btn-operator" data-operator="*">×</button>

                    <button class="btn btn-number" data-number="4">4</button>
                    <button class="btn btn-number" data-number="5">5</button>
                    <button class="btn btn-number" data-number="6">6</button>
                    <button class="btn btn-operator" data-operator="-">-</button>

                    <button class="btn btn-number" data-number="1">1</button>
                    <button class="btn btn-number" data-number="2">2</button>
                    <button class="btn btn-number" data-number="3">3</button>
                    <button class="btn btn-operator" data-operator="+">+</button>

                    <button class="btn btn-number" data-number="0" style="grid-column: span 2;">0</button>
                    <button class="btn btn-number" data-number=".">.</button>
                    <button class="btn btn-equals" data-action="calculate">=</button>
                </div>
            </div>

            <!-- History Panel -->
            <div class="calculator-history">
                <div class="history-header">
                    <h3>Riwayat</h3>
                    <button class="btn-clear-history" id="clearHistory">Hapus</button>
                </div>
                <div class="history-list" id="historyList">
                    <!-- History items will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="index.php" class="btn-nav">← Kembali ke Beranda</a>
            <a href="konversi_suhu.php" class="btn-nav">Konversi Suhu →</a>
        </div>
    </div>

    <script src="calculator.js"></script>
</body>
</html>
