<?php
require_once 'functions.php';

$hasil = '';
$nilai = '';
$dari = '';
$ke = '';
$error = '';

if ($_POST) {
    $nilai = $_POST['nilai'] ?? '';
    $dari = $_POST['dari'] ?? '';
    $ke = $_POST['ke'] ?? '';
    
    if (validasiAngka($nilai) && !empty($dari) && !empty($ke)) {
        $hasil = konversiSuhu($nilai, $dari, $ke);
        if (is_numeric($hasil)) {
            $hasil = formatAngka($hasil);
        }
    } else {
        $error = "Mohon lengkapi semua field dengan benar";
    }
}

// Array untuk nama unit suhu
$unitSuhu = [
    'celsius' => 'Celsius (°C)',
    'fahrenheit' => 'Fahrenheit (°F)',
    'kelvin' => 'Kelvin (K)'
];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Konversi Suhu - Aplikasi Kalkulator & Konversi Suhu</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🌡️ Konversi Suhu</h1>
            <nav class="breadcrumb">
                <a href="index.php">Beranda</a> > Konversi Suhu
            </nav>
        </header>

        <div class="converter-container">
            <form method="POST" class="converter-form">
                <div class="input-group">
                    <label for="nilai">Nilai Suhu:</label>
                    <input type="number" 
                           id="nilai" 
                           name="nilai" 
                           step="any" 
                           value="<?php echo htmlspecialchars($nilai); ?>" 
                           placeholder="Masukkan nilai suhu"
                           required>
                </div>

                <div class="conversion-row">
                    <div class="input-group">
                        <label for="dari">Dari:</label>
                        <select id="dari" name="dari" required>
                            <option value="">Pilih Unit</option>
                            <?php foreach ($unitSuhu as $key => $label): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($dari == $key) ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="conversion-arrow">→</div>

                    <div class="input-group">
                        <label for="ke">Ke:</label>
                        <select id="ke" name="ke" required>
                            <option value="">Pilih Unit</option>
                            <?php foreach ($unitSuhu as $key => $label): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($ke == $key) ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <button type="submit" class="btn-convert">Konversi</button>
            </form>

            <?php if ($error): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($hasil !== '' && !$error): ?>
                <div class="result-container">
                    <h3>Hasil Konversi:</h3>
                    <div class="result">
                        <?php echo htmlspecialchars($nilai); ?> <?php echo $unitSuhu[$dari]; ?> 
                        = <strong><?php echo htmlspecialchars($hasil); ?> <?php echo $unitSuhu[$ke]; ?></strong>
                    </div>
                </div>
            <?php endif; ?>

            <div class="info-box">
                <h4>Informasi Konversi Suhu:</h4>
                <ul>
                    <li><strong>Celsius (°C):</strong> Skala suhu yang umum digunakan, titik beku air = 0°C</li>
                    <li><strong>Fahrenheit (°F):</strong> Skala suhu yang digunakan di Amerika, titik beku air = 32°F</li>
                    <li><strong>Kelvin (K):</strong> Skala suhu absolut, titik nol absolut = 0K (-273.15°C)</li>
                </ul>
            </div>
        </div>

        <div class="navigation">
            <a href="kalkulator.php" class="btn-nav">← Kalkulator</a>
            <a href="index.php" class="btn-nav">Kembali ke Beranda</a>
        </div>
    </div>
</body>
</html>
