/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Breadcrumb Navigation */
.breadcrumb {
    margin-top: 15px;
    font-size: 1rem;
}

.breadcrumb a {
    color: #fff;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.breadcrumb a:hover {
    opacity: 1;
    text-decoration: underline;
}

/* Main Navigation Cards */
.main-nav {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    width: 100%;
    max-width: 800px;
}

.nav-card {
    background: white;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    text-decoration: none;
    color: #333;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.nav-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    border-color: #667eea;
}

.card-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.nav-card h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #667eea;
}

.nav-card p {
    color: #666;
    line-height: 1.5;
}

/* Form Styles */
.calculator-container,
.converter-container {
    background: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.calculator-form,
.converter-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.input-group input,
.input-group select {
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Conversion Row */
.conversion-row {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: end;
}

.conversion-arrow {
    font-size: 2rem;
    color: #667eea;
    text-align: center;
    margin-bottom: 10px;
}

/* Buttons */
.btn-calculate,
.btn-convert {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-calculate:hover,
.btn-convert:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Result Styles */
.result-container {
    background: #f8f9ff;
    border: 2px solid #667eea;
    border-radius: 10px;
    padding: 25px;
    margin-top: 25px;
    text-align: center;
}

.result-container h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.result {
    font-size: 1.5rem;
    color: #333;
    font-weight: 500;
}

.result strong {
    color: #667eea;
    font-weight: 700;
}

/* Error Message */
.error-message {
    background: #ffe6e6;
    border: 2px solid #ff4757;
    color: #c44569;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: center;
    font-weight: 500;
}

/* Info Box */
.info-box {
    background: #f0f8ff;
    border: 2px solid #667eea;
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
}

.info-box h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-box ul {
    list-style: none;
    padding-left: 0;
}

.info-box li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.info-box li:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Navigation */
.navigation {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: auto;
    padding-top: 30px;
}

.btn-nav {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 12px 25px;
    border-radius: 25px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.btn-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Footer */
footer {
    text-align: center;
    margin-top: auto;
    padding-top: 40px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .nav-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .nav-card {
        padding: 30px 20px;
    }
    
    .calculator-container,
    .converter-container {
        padding: 25px;
    }
    
    .conversion-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .conversion-arrow {
        transform: rotate(90deg);
        margin: 0;
    }
    
    .navigation {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn-nav {
        text-align: center;
    }
}
